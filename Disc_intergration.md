# DISC Assessment Implementation Documentation

## Overview

The DISC Assessment feature is a comprehensive behavioral analysis system integrated into the existing skills gap analysis platform. It provides users with detailed personality insights based on the DISC methodology (Dominance, Influence, Steadiness, Conscientiousness) to support professional development and workplace effectiveness.

### Key Features
- **Behavioral Assessment**: 5-6 forced-choice questions analyzing workplace scenarios
- **AI-Powered Analysis**: OpenAI o3-mini model for question generation and response analysis
- **Integrated Display**: Seamless integration within the skills gap analysis modal
- **Professional Insights**: Comprehensive reports covering strengths, challenges, communication style, and development recommendations

### Benefits for Users
- **Self-Awareness**: Deep understanding of behavioral preferences and tendencies
- **Professional Development**: Targeted recommendations for career growth
- **Team Dynamics**: Insights into collaboration and leadership styles
- **Communication Enhancement**: Understanding of preferred communication approaches

## Technical Architecture

### Question Distribution Changes
The assessment maintains a total of 15-16 questions with the following distribution:
- **Knowledge-check questions**: 7 (reduced from 10)
- **Self-assessment questions**: 3 (reduced from 5)
- **DISC assessment questions**: 5-6 (newly added)

### Data Flow
1. **Question Generation**: DISC questions generated via OpenAI API and cached in Firestore
2. **Response Collection**: User responses stored in `window.discResponses` array
3. **Processing**: Responses analyzed by OpenAI to determine personality profile
4. **Storage**: DISC profile stored in user document in Firestore
5. **Display**: Profile integrated into skills gap modal with unified layout

## File Structure and Modifications

### Backend Files

#### `server.js`
**New API Endpoints:**
- `/api/generate-disc-questions`: Generates DISC behavioral scenario questions
- `/api/process-disc-assessment`: Analyzes responses and creates personality profile

**New Functions:**
- `analyzeDiscResponses()`: Processes DISC responses using OpenAI
- `updateUserDiscProfile()`: Stores DISC profile in Firestore

### Frontend Files

#### `public/quizFunctions.js`
**Key Modifications:**
- Updated question distribution logic for DISC integration
- Enhanced `endQuiz()` function with DISC processing
- Progressive loading system for mixed question types

#### `public/script2.js`
**Key Changes:**
- DISC response collection in button click handlers
- Enhanced `fetchAssessmentData()` to include user company information
- Response storage in `window.discResponses` array

#### `public/skills-gap-modal.js`
**Major Additions:**
- `loadDiscProfile()`: Fetches DISC data from Firestore
- `displayDiscProfile()`: Renders DISC profile with unified layout
- `formatDiscReport()`: Formats detailed analysis content
- `toggleDiscDetails()`: Handles expand/collapse functionality
- Polling mechanism for real-time profile updates

#### `public/style.css`
**New Styling:**
- DISC profile display components
- Unified layout integration
- Responsive design for mobile devices
- Professional color scheme and typography

### Test Files

#### `public/disc-test.html`
**Testing Interface:**
- DISC question generation testing
- Response processing validation
- Database query verification
- Debug information display

## Database Schema

### User Document Structure
```javascript
{
  // Existing user fields...
  discProfile: {
    primaryType: "D" | "I" | "S" | "C",
    scores: {
      D: 50,  // Percentage
      I: 33,
      S: 17,
      C: 0
    },
    detailedReport: "Comprehensive analysis text...",
    timestamp: FirebaseTimestamp,
    status: "completed" | "processing" | "pending"
  }
}
```

### Question Caching
```javascript
// Collection: questionCache_disc
{
  questionId: "unique_id",
  question: "Behavioral scenario question...",
  options: ["Option A", "Option B", "Option C", "Option D"],
  discTraits: ["D", "I", "S", "C"],
  scenario: "leadership" | "communication" | "teamwork" | "analysis",
  type: "disc",
  createdAt: FirebaseTimestamp
}
```

## API Documentation

### Generate DISC Questions
**Endpoint:** `POST /api/generate-disc-questions`

**Request:**
```javascript
{
  role: "Manager",
  section: "Intermediate", 
  framework: "Framework Name",
  email: "<EMAIL>",
  batchSize: 5
}
```

**Response:**
```javascript
[
  {
    question: "Behavioral scenario question...",
    options: ["Option A", "Option B", "Option C", "Option D"],
    discTraits: ["D", "I", "S", "C"],
    scenario: "leadership",
    type: "disc"
  }
]
```

### Process DISC Assessment
**Endpoint:** `POST /api/process-disc-assessment`

**Request:**
```javascript
{
  userEmail: "<EMAIL>",
  userCompany: "Company Name",
  discResponses: [
    {
      questionId: 1,
      question: "Question text...",
      selectedOption: "Selected option text",
      discTrait: "D",
      scenario: "leadership",
      timestamp: "ISO string"
    }
  ],
  role: "Manager"
}
```

**Response:**
```javascript
{
  success: true,
  message: "DISC assessment processed successfully",
  discProfile: {
    primaryType: "D",
    scores: { D: 50, I: 33, S: 17, C: 0 },
    detailedReport: "Analysis text...",
    timestamp: FirebaseTimestamp,
    status: "completed"
  }
}
```

## Skills Gap Modal Integration

### Overview
The DISC profile is seamlessly integrated into the existing skills gap analysis modal using a unified layout approach that eliminates visual disconnect between the summary and detailed behavioral analysis. This integration ensures a cohesive user experience while providing comprehensive personality insights.

### Data Integration Flow

#### 1. User Company Resolution
The integration begins by ensuring the correct company context is available for database queries:

```javascript
// In fetchAssessmentData() function (script2.js)
return {
  report: {
    competencyAnalysis: assessmentData.competencyAnalysis,
    summary: assessmentData.analysisSummary,
    employeeEmail: userEmail,        // Added for DISC integration
    userCompany: userCompany         // Critical for correct database queries
  },
  recommendations: assessmentData.courseRecommendations.map(rec => ({
    course: rec.courseName,
    reason: rec.justification
  })),
  // ... rest of structure
};
```

#### 2. Modal Initialization with DISC Loading
When the skills gap modal opens, it automatically loads the DISC profile:

```javascript
// In showSkillsGapAnalysis() function (skills-gap-modal.js)
async function showSkillsGapAnalysis(data = null) {
    // ... modal setup code ...

    // Load DISC profile if user data is available
    if (currentData && currentData.report && currentData.report.employeeEmail) {
        const userCompany = currentData.report.userCompany || 'Birmingham';
        console.log('Skills gap modal loading DISC profile:', {
            email: currentData.report.employeeEmail,
            userCompany: userCompany,
            hasUserCompanyInData: !!currentData.report.userCompany
        });
        await loadDiscProfile(currentData.report.employeeEmail, userCompany);

        // Start polling for DISC profile updates if still processing
        startDiscProfilePolling(currentData.report.employeeEmail, userCompany);
    }
}
```

### Core Integration Functions

#### 1. DISC Profile Data Fetching
```javascript
async function loadDiscProfile(email, userCompany) {
    const discContent = document.getElementById('disc-profile-content');
    if (!discContent) {
        console.error('DISC content element not found');
        return;
    }

    console.log('Loading DISC profile for:', { email, userCompany });

    try {
        // Check if user has a DISC profile
        const db = firebase.firestore();
        const userRef = db.collection('companies').doc(userCompany).collection('users').doc(email);
        const userDoc = await userRef.get();

        if (userDoc.exists) {
            const userData = userDoc.data();
            const discProfile = userData.discProfile;

            console.log('User document found, DISC profile:', discProfile);

            if (discProfile && discProfile.status === 'completed') {
                console.log('Displaying completed DISC profile:', discProfile.primaryType);
                displayDiscProfile(discProfile);
            } else if (discProfile && discProfile.status === 'processing') {
                console.log('DISC profile is still processing');
                // Show processing state with refresh button
                discContent.innerHTML = `
                    <div class="disc-loading">
                        <div class="disc-spinner"></div>
                        <p>Analyzing behavioral assessment...</p>
                        <button onclick="window.refreshDiscProfile && window.refreshDiscProfile()"
                                style="margin-top: 10px; padding: 5px 10px; background: #007cba;
                                       color: white; border: none; border-radius: 3px; cursor: pointer;">
                            Refresh
                        </button>
                    </div>
                `;
            } else {
                console.log('No DISC profile found or incomplete status:', discProfile?.status);
                discContent.innerHTML = `
                    <div class="disc-pending">
                        <p>DISC behavioral assessment not completed</p>
                    </div>
                `;
            }
        } else {
            console.log('User document does not exist');
            discContent.innerHTML = `
                <div class="disc-pending">
                    <p>User profile not found</p>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading DISC profile:', error);
        discContent.innerHTML = `
            <div class="disc-pending">
                <p>Error loading DISC profile: ${error.message}</p>
            </div>
        `;
    }
}
```

#### 2. DISC Profile Display with Unified Layout
```javascript
function displayDiscProfile(discProfile) {
    const discContent = document.getElementById('disc-profile-content');
    if (!discContent) {
        console.error('DISC content element not found in displayDiscProfile');
        return;
    }

    const primaryType = discProfile.primaryType;
    console.log('Displaying DISC profile for primary type:', primaryType);

    const typeDescriptions = {
        'D': 'Dominance - Direct, decisive, and results-oriented',
        'I': 'Influence - Inspiring, enthusiastic, and people-focused',
        'S': 'Steadiness - Supportive, reliable, and team-oriented',
        'C': 'Conscientiousness - Careful, analytical, and quality-focused'
    };

    if (!typeDescriptions[primaryType]) {
        console.error('Invalid primary type:', primaryType);
        discContent.innerHTML = `
            <div class="disc-pending">
                <p>Invalid DISC profile data</p>
            </div>
        `;
        return;
    }

    const fullTypeName = primaryType === 'D' ? 'Dominance' :
                        primaryType === 'I' ? 'Influence' :
                        primaryType === 'S' ? 'Steadiness' : 'Conscientiousness';

    // Store the full profile data for the expand/collapse functionality
    discContent.dataset.discProfile = JSON.stringify(discProfile);

    // Unified layout structure - everything in one container
    discContent.innerHTML = `
        <div class="disc-profile-completed">
            <div class="disc-badge ${primaryType}">${primaryType}</div>
            <div class="disc-info">
                <h4>Primary Type: ${primaryType} - ${fullTypeName}</h4>
                <p>${typeDescriptions[primaryType]}</p>
                <div class="disc-scores">
                    <div class="score-item">
                        <span class="score-label">Dominance</span>
                        <span class="score-value">${discProfile.scores?.D || 0}%</span>
                    </div>
                    <div class="score-item">
                        <span class="score-label">Influence</span>
                        <span class="score-value">${discProfile.scores?.I || 0}%</span>
                    </div>
                    <div class="score-item">
                        <span class="score-label">Steadiness</span>
                        <span class="score-value">${discProfile.scores?.S || 0}%</span>
                    </div>
                    <div class="score-item">
                        <span class="score-label">Conscientiousness</span>
                        <span class="score-value">${discProfile.scores?.C || 0}%</span>
                    </div>
                </div>
                <button class="disc-learn-more" onclick="toggleDiscDetails()">Learn More</button>
                <div id="disc-detailed-content" class="disc-detailed-content" style="display: none;">
                    ${formatDiscReport(discProfile.detailedReport, primaryType, fullTypeName)}
                </div>
            </div>
        </div>
    `;

    console.log('DISC profile display completed successfully');
}
```

#### 3. Content Formatting for Detailed Analysis
```javascript
function formatDiscReport(detailedReport, primaryType, fullTypeName) {
    if (!detailedReport) {
        return '<div class="disc-analysis-intro"><h5>Detailed Analysis</h5><p>No detailed report available</p></div>';
    }

    console.log('Formatting DISC report, length:', detailedReport.length);

    // Define section icons and their mappings
    const sectionIcons = {
        'OVERVIEW': '👤', 'STRENGTHS': '💪', 'POTENTIAL CHALLENGES': '⚠️', 'CHALLENGES': '⚠️',
        'COMMUNICATION STYLE': '💬', 'COMMUNICATION': '💬', 'WORK ENVIRONMENT': '🏢', 'WORK': '🏢',
        'LEADERSHIP STYLE': '👑', 'LEADERSHIP': '👑', 'TEAM DYNAMICS': '🤝', 'TEAM': '🤝',
        'PROFESSIONAL DEVELOPMENT': '📈', 'DEVELOPMENT': '📈'
    };

    // Start with a simple intro that integrates with the existing layout
    let formattedReport = `
        <div class="disc-analysis-intro">
            <h5>Detailed Behavioral Analysis</h5>
            <p>Comprehensive insights into your ${fullTypeName} personality profile</p>
        </div>
    `;

    // Split the report into sections and format them
    const sections = detailedReport.split(/\n\s*\n/);

    sections.forEach((section, index) => {
        const lines = section.trim().split('\n');
        if (lines.length === 0) return;

        const firstLine = lines[0].trim();

        // Check if this looks like a section header
        const headerMatch = firstLine.match(/^\d+\.\s*(.*?)$/i) ||
                           firstLine.match(/^(OVERVIEW|STRENGTHS|POTENTIAL CHALLENGES|CHALLENGES|COMMUNICATION STYLE|COMMUNICATION|WORK ENVIRONMENT|WORK|LEADERSHIP STYLE|LEADERSHIP|TEAM DYNAMICS|TEAM|PROFESSIONAL DEVELOPMENT|DEVELOPMENT).*$/i);

        if (headerMatch) {
            const sectionTitle = (headerMatch[1] || headerMatch[0]).toUpperCase().trim();
            const cleanTitle = sectionTitle.replace(/^\d+\.\s*/, '');
            const displayTitle = cleanTitle.toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
            const icon = sectionIcons[cleanTitle] || sectionIcons[Object.keys(sectionIcons).find(key => cleanTitle.includes(key))] || '📋';

            formattedReport += `
                <div class="disc-section">
                    <div class="disc-section-header">
                        <div class="disc-section-icon">${icon}</div>
                        <h4 class="disc-section-title">${displayTitle}</h4>
                    </div>
            `;

            if (lines.length > 1) {
                const content = lines.slice(1).join('\n').trim();
                formattedReport += `<div class="disc-section-content">${formatSectionContent(content)}</div>`;
            }

            formattedReport += `</div>`;
        } else if (section.trim().length > 0) {
            // Regular content without a clear header
            formattedReport += `
                <div class="disc-section">
                    <div class="disc-section-header">
                        <div class="disc-section-icon">📝</div>
                        <h4 class="disc-section-title">Additional Insights</h4>
                    </div>
                    <div class="disc-section-content">${formatSectionContent(section)}</div>
                </div>
            `;
        }
    });

    console.log('Formatted DISC report sections:', sections.length);
    return formattedReport;
}

function formatSectionContent(content) {
    if (!content) return '';

    // Split content into paragraphs
    const paragraphs = content.split(/\n\s*\n/);
    let formattedContent = '';

    paragraphs.forEach(paragraph => {
        const trimmed = paragraph.trim();
        if (!trimmed) return;

        // Check if this paragraph contains bullet points
        const lines = trimmed.split('\n');
        const hasBullets = lines.some(line => line.trim().match(/^[-•*]\s+/));

        if (hasBullets) {
            // Format as a list
            formattedContent += '<ul>';
            lines.forEach(line => {
                const trimmedLine = line.trim();
                if (trimmedLine.match(/^[-•*]\s+/)) {
                    const listItem = trimmedLine.replace(/^[-•*]\s+/, '');
                    formattedContent += `<li>${formatInlineText(listItem)}</li>`;
                } else if (trimmedLine) {
                    // Non-bullet line in a bullet context, treat as continuation
                    formattedContent += `<li>${formatInlineText(trimmedLine)}</li>`;
                }
            });
            formattedContent += '</ul>';
        } else {
            // Format as regular paragraph
            const formattedParagraph = lines
                .map(line => line.trim())
                .filter(line => line)
                .join(' ');

            if (formattedParagraph) {
                formattedContent += `<p>${formatInlineText(formattedParagraph)}</p>`;
            }
        }
    });

    return formattedContent;
}

function formatInlineText(text) {
    return text
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/`(.*?)`/g, '<code>$1</code>')
        .replace(/\b(DISC|D|I|S|C)\b/g, '<strong>$1</strong>');
}
```

#### 4. Toggle Functionality for Detailed Analysis
```javascript
// Global function for toggling DISC details
window.toggleDiscDetails = function() {
    const detailsContent = document.getElementById('disc-detailed-content');
    const button = document.querySelector('.disc-learn-more');

    if (!detailsContent || !button) {
        console.error('DISC details elements not found', {
            detailsContent: !!detailsContent,
            button: !!button
        });
        return;
    }

    const isExpanded = detailsContent.style.display !== 'none';
    console.log('Toggling DISC details, currently expanded:', isExpanded);

    if (isExpanded) {
        // Collapse
        detailsContent.style.display = 'none';
        button.textContent = 'Learn More';
        button.classList.remove('expanded');
        console.log('DISC details collapsed');
    } else {
        // Expand
        detailsContent.style.display = 'block';
        button.textContent = 'Show Less';
        button.classList.add('expanded');
        console.log('DISC details expanded');

        // Smooth scroll to show the expanded content
        setTimeout(() => {
            detailsContent.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest'
            });
        }, 100);
    }
};
```

#### 5. Real-Time Polling System
```javascript
let discPollingInterval = null;

// Global refresh function for manual testing
window.refreshDiscProfile = async function() {
    if (currentData && currentData.report && currentData.report.employeeEmail) {
        const userCompany = currentData.report.userCompany || 'Birmingham';
        console.log('Manual DISC profile refresh triggered');
        await loadDiscProfile(currentData.report.employeeEmail, userCompany);
    }
};

function startDiscProfilePolling(email, userCompany) {
    // Clear any existing polling
    if (discPollingInterval) {
        clearInterval(discPollingInterval);
    }

    // Check if DISC profile is still processing
    const discContent = document.getElementById('disc-profile-content');
    if (!discContent) return;

    const isProcessing = discContent.innerHTML.includes('Processing behavioral assessment') ||
                       discContent.innerHTML.includes('Analyzing behavioral assessment');

    if (isProcessing) {
        console.log('Starting DISC profile polling...');

        discPollingInterval = setInterval(async () => {
            console.log('Polling for DISC profile updates...');
            await loadDiscProfile(email, userCompany);

            // Stop polling if profile is now completed or failed
            const updatedContent = document.getElementById('disc-profile-content');
            if (updatedContent && !updatedContent.innerHTML.includes('disc-spinner')) {
                console.log('DISC profile polling completed');
                clearInterval(discPollingInterval);
                discPollingInterval = null;
            }
        }, 3000); // Poll every 3 seconds

        // Stop polling after 2 minutes to prevent infinite polling
        setTimeout(() => {
            if (discPollingInterval) {
                console.log('DISC profile polling timeout reached');
                clearInterval(discPollingInterval);
                discPollingInterval = null;
            }
        }, 120000); // 2 minutes
    }
}

// Clean up polling when modal is closed
async function hideModal() {
    // ... existing modal cleanup code ...

    // Clean up DISC polling
    if (discPollingInterval) {
        clearInterval(discPollingInterval);
        discPollingInterval = null;
    }

    // ... rest of cleanup code ...
}
```

### CSS Styling for Unified Integration

#### Core DISC Profile Styles
```css
/* Unified DISC Details Styles - Seamless Integration */
.disc-detailed-content {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
    animation: fadeIn 0.3s ease-in-out;
}

.disc-analysis-intro {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    text-align: center;
}

.disc-analysis-intro h5 {
    margin: 0 0 0.5rem 0;
    color: #1e293b;
    font-size: 1rem;
    font-weight: 600;
}

.disc-analysis-intro p {
    margin: 0;
    color: #64748b;
    font-size: 0.875rem;
}
```

#### Section Styling for Seamless Flow
```css
.disc-section {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #f1f5f9;
}

.disc-section:last-child {
    margin-bottom: 0;
    border-bottom: none;
    padding-bottom: 0;
}

.disc-section-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
}

.disc-section-icon {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.7rem;
    flex-shrink: 0;
}

.disc-section-title {
    color: #1e293b;
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
    letter-spacing: 0.025em;
}

.disc-section-content {
    color: #475569;
    line-height: 1.6;
    font-size: 0.9rem;
    margin-left: 2.25rem;
}
```

#### Enhanced Scores Display
```css
.disc-scores {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.75rem;
    margin-top: 1rem;
    padding: 1rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.score-item {
    background: white;
    padding: 0.75rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 600;
    color: #1e293b;
    border: 1px solid #e2e8f0;
    text-align: center;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease-in-out;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.score-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.08);
}

.score-item .score-label {
    font-size: 0.75rem;
    color: #64748b;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.score-item .score-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: #3b82f6;
}
```

#### Interactive Button Styling
```css
.disc-learn-more {
    transition: all 0.3s ease-in-out;
    position: relative;
    overflow: hidden;
}

.disc-learn-more.expanded {
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    transform: scale(0.98);
}

.disc-learn-more.expanded:hover {
    background: linear-gradient(135deg, #475569 0%, #334155 100%);
    transform: scale(1);
}

.disc-learn-more:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.disc-learn-more:hover:before {
    left: 100%;
}
```

#### Responsive Design
```css
@media (max-width: 768px) {
    .disc-section-content {
        margin-left: 1.5rem;
        font-size: 0.85rem;
    }

    .disc-section-header {
        gap: 0.5rem;
    }

    .disc-section-icon {
        width: 18px;
        height: 18px;
        font-size: 0.65rem;
    }

    .disc-section-title {
        font-size: 0.9rem;
    }

    .disc-scores {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
        padding: 0.75rem;
    }

    .score-item {
        padding: 0.5rem;
    }

    .disc-analysis-intro {
        padding: 0.75rem;
    }

    .disc-analysis-intro h5 {
        font-size: 0.9rem;
    }

    .disc-analysis-intro p {
        font-size: 0.8rem;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-15px) scale(0.98);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}
```

## Error Handling and Edge Cases

### Missing DISC Profile Handling
```javascript
// Graceful handling of missing profiles
if (!discProfile) {
    discContent.innerHTML = `
        <div class="disc-pending">
            <p>DISC behavioral assessment not completed</p>
            <small>Complete a skills assessment to generate your DISC profile</small>
        </div>
    `;
    return;
}

// Invalid profile data handling
if (!discProfile.primaryType || !discProfile.scores) {
    discContent.innerHTML = `
        <div class="disc-pending">
            <p>Invalid DISC profile data</p>
            <button onclick="window.refreshDiscProfile && window.refreshDiscProfile()"
                    class="refresh-btn">Retry Loading</button>
        </div>
    `;
    return;
}
```

### Loading States and Progress Indicators
```javascript
// Processing state with spinner
discContent.innerHTML = `
    <div class="disc-loading">
        <div class="disc-spinner"></div>
        <p>Analyzing behavioral assessment...</p>
        <div class="progress-indicator">
            <div class="progress-bar"></div>
        </div>
        <button onclick="window.refreshDiscProfile && window.refreshDiscProfile()"
                style="margin-top: 10px; padding: 5px 10px; background: #007cba;
                       color: white; border: none; border-radius: 3px; cursor: pointer;">
            Refresh
        </button>
    </div>
`;
```

### Network Error Handling
```javascript
try {
    const userDoc = await userRef.get();
    // ... processing logic ...
} catch (error) {
    console.error('Error loading DISC profile:', error);
    discContent.innerHTML = `
        <div class="disc-error">
            <p>Error loading DISC profile</p>
            <small>${error.message}</small>
            <button onclick="window.refreshDiscProfile && window.refreshDiscProfile()"
                    class="retry-btn">Try Again</button>
        </div>
    `;
}
```

### Fallback Content for Missing Reports
```javascript
function formatDiscReport(detailedReport, primaryType, fullTypeName) {
    if (!detailedReport) {
        return `
            <div class="disc-analysis-intro">
                <h5>Detailed Analysis</h5>
                <p>Detailed behavioral analysis is being generated. Please check back shortly.</p>
                <button onclick="window.refreshDiscProfile && window.refreshDiscProfile()"
                        class="refresh-btn">Refresh</button>
            </div>
        `;
    }
    // ... rest of formatting logic ...
}
```

## Frontend Components

### Response Collection
```javascript
// Store DISC response in script2.js
window.discResponses.push({
  questionId: questionObj.id,
  question: questionObj.question,
  selectedOption: selectedOption.innerText,
  discTrait: discTrait,
  scenario: scenario,
  timestamp: new Date().toISOString()
});
```

### State Management
- **Loading States**: Spinner during processing with progress indicators
- **Error Handling**: Graceful fallbacks for missing data with retry mechanisms
- **Polling**: Automatic refresh for profile updates with timeout protection
- **Toggle State**: Expand/collapse functionality with smooth animations
- **Data Persistence**: Profile data cached in DOM for quick access

## Testing and Debugging

### Test Interface (`/disc-test.html`)
1. **Question Generation Test**: Verify DISC question creation
2. **Processing Test**: Validate response analysis
3. **Database Query Test**: Check profile storage and retrieval
4. **Formatting Test**: Preview detailed report layout

### Debug Features
- Console logging throughout the pipeline
- Manual refresh buttons for testing
- Error message display
- Response validation checks

## Deployment Considerations

### Environment Variables
- OpenAI API key configuration
- Firebase project settings
- Model selection (o3-mini)

### Performance Optimization
- Question caching to reduce API calls
- Efficient Firestore queries
- Lazy loading of detailed content
- Responsive design for mobile devices

### Security
- Input validation and sanitization
- Rate limiting on API endpoints
- User authentication verification
- Data privacy compliance

## Future Enhancements

### Potential Improvements
- Team DISC analysis and comparison
- Historical profile tracking
- Integration with learning recommendations
- Advanced reporting and analytics
- Export functionality for profiles

### Scalability Considerations
- Batch processing for multiple users
- Advanced caching strategies
- Performance monitoring
- Load balancing for high traffic

---

*This documentation provides a comprehensive overview of the DISC assessment implementation. For specific code examples and detailed function documentation, refer to the individual source files.*